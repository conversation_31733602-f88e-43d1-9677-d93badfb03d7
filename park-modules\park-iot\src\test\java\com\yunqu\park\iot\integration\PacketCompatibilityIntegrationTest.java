package com.yunqu.park.iot.integration;

import com.yunqu.park.iot.config.IotProtocolConfig;
import com.yunqu.park.iot.protocol.ProtocolPacketBuilder;
import com.yunqu.park.iot.utils.CrcUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据包兼容性集成测试
 * 
 * <p>测试修复后的数据包与concox-master的完全兼容性。</p>
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2025-08-07
 */
@SpringBootTest
@TestPropertySource(properties = {
    "iot.protocol.compatibility-mode=CONCOX",
    "iot.protocol.crc.enabled=true",
    "iot.protocol.crc.algorithm=crc-itu"
})
class PacketCompatibilityIntegrationTest {

    private ProtocolPacketBuilder packetBuilder;
    private IotProtocolConfig protocolConfig;

    @BeforeEach
    void setUp() {
        protocolConfig = new IotProtocolConfig();
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.CONCOX);
        protocolConfig.getCrc().setEnabled(true);
        protocolConfig.getCrc().setAlgorithm("crc-itu");
        
        packetBuilder = new ProtocolPacketBuilder(protocolConfig);
    }

    @Test
    void testRebootCommandCompatibility() {
        // 测试REBOOT指令的完整兼容性
        String command = "RESET#";
        byte[] packet = packetBuilder.buildCommandPacket(command);
        
        assertNotNull(packet, "数据包不应为null");
        assertTrue(packet.length > 0, "数据包长度应大于0");
        
        String hexPacket = packetBuilder.bytesToHexString(packet);
        System.out.println("REBOOT Command Packet: " + hexPacket);
        
        // 验证数据包结构
        validateConcoxPacketStructure(hexPacket, command);
        
        // 验证CRC
        validateCrcCompatibility(hexPacket);
    }

    @Test
    void testQueryStatusCommandCompatibility() {
        // 测试QUERY_STATUS指令的完整兼容性
        String command = "STATUS#";
        byte[] packet = packetBuilder.buildCommandPacket(command);
        
        assertNotNull(packet, "数据包不应为null");
        assertTrue(packet.length > 0, "数据包长度应大于0");
        
        String hexPacket = packetBuilder.bytesToHexString(packet);
        System.out.println("STATUS Command Packet: " + hexPacket);
        
        // 验证数据包结构
        validateConcoxPacketStructure(hexPacket, command);
        
        // 验证CRC
        validateCrcCompatibility(hexPacket);
    }

    @Test
    void testMultipleCommandsSequence() {
        // 测试多个指令的序列号递增
        String[] commands = {"RESET#", "STATUS#", "VERSION#", "ICCID#"};
        String[] packets = new String[commands.length];
        
        for (int i = 0; i < commands.length; i++) {
            byte[] packet = packetBuilder.buildCommandPacket(commands[i]);
            packets[i] = packetBuilder.bytesToHexString(packet);
            System.out.println("Command " + (i + 1) + " (" + commands[i] + "): " + packets[i]);
        }
        
        // 验证所有数据包都有效
        for (String packet : packets) {
            assertNotNull(packet, "数据包不应为null");
            assertTrue(packet.length() > 0, "数据包长度应大于0");
            validateBasicPacketStructure(packet);
        }
    }

    @Test
    void testCrcCalculationAccuracy() {
        // 测试CRC计算的准确性
        String[] testData = {
            "12800a000000005245534554230020001",  // RESET# 示例
            "13800b00000000535441545553230020001", // STATUS# 示例
        };
        
        for (String data : testData) {
            int crc = CrcUtils.calculateCrcItuFromHexString(data);
            
            // 验证CRC值有效性
            assertTrue(crc >= 0 && crc <= 0xFFFF, "CRC值应在有效范围内");
            assertNotEquals(0, crc, "CRC值不应为0（除非数据确实产生0值）");
            
            // 验证CRC验证功能
            String fullPacket = data + String.format("%04x", crc);
            assertTrue(CrcUtils.validateConcoxCrc(fullPacket), "CRC验证应该通过");
            
            System.out.println("Data: " + data);
            System.out.println("CRC: 0x" + String.format("%04X", crc));
            System.out.println("Full packet: " + fullPacket);
            System.out.println("---");
        }
    }

    @Test
    void testPacketSizeConsistency() {
        // 测试相同指令的数据包大小一致性
        String command = "RESET#";
        
        byte[] packet1 = packetBuilder.buildCommandPacket(command);
        byte[] packet2 = packetBuilder.buildCommandPacket(command);
        
        // 数据包长度应该一致（除了序列号可能不同）
        assertEquals(packet1.length, packet2.length, "相同指令的数据包长度应该一致");
        
        String hex1 = packetBuilder.bytesToHexString(packet1);
        String hex2 = packetBuilder.bytesToHexString(packet2);
        
        System.out.println("Packet 1: " + hex1);
        System.out.println("Packet 2: " + hex2);
        
        // 验证基本结构相同（起始位、协议号、指令内容、停止位）
        assertEquals(hex1.substring(0, 8), hex2.substring(0, 8), "起始位和包长度应该相同");
        assertTrue(hex1.endsWith("0D0A"), "数据包1应以0D0A结束");
        assertTrue(hex2.endsWith("0D0A"), "数据包2应以0D0A结束");
    }

    @Test
    void testErrorHandling() {
        // 测试错误处理
        String[] invalidCommands = {null, "", "   ", "INVALID_COMMAND_WITHOUT_HASH"};
        
        for (String invalidCommand : invalidCommands) {
            byte[] packet = packetBuilder.buildCommandPacket(invalidCommand);
            assertEquals(0, packet.length, 
                        "无效指令应该返回空数组: " + invalidCommand);
        }
    }

    @Test
    void testConfigurationModes() {
        // 测试不同配置模式
        
        // 测试CONCOX模式
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.CONCOX);
        byte[] concoxPacket = packetBuilder.buildCommandPacket("RESET#");
        String concoxHex = packetBuilder.bytesToHexString(concoxPacket);
        
        // 测试STANDARD模式
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.STANDARD);
        byte[] standardPacket = packetBuilder.buildCommandPacket("RESET#");
        String standardHex = packetBuilder.bytesToHexString(standardPacket);
        
        // 两种模式应该产生不同的数据包
        assertNotEquals(concoxHex, standardHex, "不同模式应该产生不同的数据包");
        
        System.out.println("CONCOX mode packet: " + concoxHex);
        System.out.println("STANDARD mode packet: " + standardHex);
        
        // 但都应该是有效的数据包
        validateBasicPacketStructure(concoxHex);
        validateBasicPacketStructure(standardHex);
    }

    /**
     * 验证Concox数据包结构
     */
    private void validateConcoxPacketStructure(String hexPacket, String originalCommand) {
        // 基本结构验证
        validateBasicPacketStructure(hexPacket);
        
        // 验证指令内容存在
        String commandHex = asciiToHex(originalCommand);
        assertTrue(hexPacket.contains(commandHex.toUpperCase()), 
                  "数据包应包含指令内容: " + commandHex);
        
        // 验证协议号
        assertTrue(hexPacket.contains("80"), "数据包应包含协议号80");
        
        // 验证语言设置（0002）
        assertTrue(hexPacket.contains("0002"), "数据包应包含语言设置0002");
    }

    /**
     * 验证基本数据包结构
     */
    private void validateBasicPacketStructure(String hexPacket) {
        assertNotNull(hexPacket, "数据包不应为null");
        assertTrue(hexPacket.length() > 0, "数据包长度应大于0");
        assertTrue(hexPacket.startsWith("7878"), "数据包应以7878开始");
        assertTrue(hexPacket.endsWith("0D0A"), "数据包应以0D0A结束");
        assertTrue(hexPacket.length() % 2 == 0, "数据包长度应为偶数（十六进制字符串）");
    }

    /**
     * 验证CRC兼容性
     */
    private void validateCrcCompatibility(String hexPacket) {
        // 移除起始位和停止位
        String dataWithCrc = hexPacket.substring(4, hexPacket.length() - 4);
        
        // 验证CRC
        boolean crcValid = CrcUtils.validateConcoxCrc(dataWithCrc);
        assertTrue(crcValid, "CRC验证应该通过: " + dataWithCrc);
    }

    /**
     * ASCII转十六进制
     */
    private String asciiToHex(String ascii) {
        StringBuilder hex = new StringBuilder();
        for (char c : ascii.toCharArray()) {
            hex.append(String.format("%02x", (int) c));
        }
        return hex.toString();
    }

    @Test
    void testPerformanceBenchmark() {
        // 性能基准测试
        String command = "RESET#";
        int iterations = 1000;
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            byte[] packet = packetBuilder.buildCommandPacket(command);
            assertNotNull(packet, "数据包不应为null");
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double avgTime = (double) duration / iterations;
        
        System.out.println("Performance Benchmark:");
        System.out.println("Iterations: " + iterations);
        System.out.println("Total time: " + duration + "ms");
        System.out.println("Average time per packet: " + String.format("%.2f", avgTime) + "ms");
        
        // 性能要求：平均每个数据包构建时间应小于1ms
        assertTrue(avgTime < 1.0, "数据包构建性能应该足够快（<1ms per packet）");
    }

    @Test
    void testMemoryUsage() {
        // 内存使用测试
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收
        System.gc();
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        // 构建大量数据包
        String command = "RESET#";
        for (int i = 0; i < 10000; i++) {
            byte[] packet = packetBuilder.buildCommandPacket(command);
            // 不保存引用，让GC回收
        }
        
        // 再次强制垃圾回收
        System.gc();
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        
        long memoryUsed = memoryAfter - memoryBefore;
        
        System.out.println("Memory Usage Test:");
        System.out.println("Memory before: " + memoryBefore + " bytes");
        System.out.println("Memory after: " + memoryAfter + " bytes");
        System.out.println("Memory used: " + memoryUsed + " bytes");
        
        // 内存使用应该合理（不应该有明显的内存泄漏）
        assertTrue(memoryUsed < 10 * 1024 * 1024, "内存使用应该合理（<10MB）");
    }
}
