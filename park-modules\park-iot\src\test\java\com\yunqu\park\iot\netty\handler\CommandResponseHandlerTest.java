package com.yunqu.park.iot.netty.handler;

import com.yunqu.park.iot.netty.protocol.IotMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * CommandResponseHandler测试类
 * 验证在线指令回复包的解析逻辑
 */
@ExtendWith(MockitoExtension.class)
public class CommandResponseHandlerTest {

    @InjectMocks
    private CommandResponseHandler commandResponseHandler;

    private IotMessage testMessage;
    private String testImei = "035399471429026";

    @BeforeEach
    void setUp() {
        testMessage = new IotMessage();
        testMessage.setProtocol((byte) 0x15);
        testMessage.setSequenceNumber(161);
    }

    @Test
    void testParseOnlineCommandResponse() {
        // 测试数据包: 78 78 0F 15 07 00 00 00 00 4F 4B 21 00 01 00 A1 89 53 0D 0A
        // content部分: 07 00 00 00 00 4F 4B 21 00 01 00
        byte[] content = {
            0x07,                           // 指令长度: 7
            0x00, 0x00, 0x00, 0x00,        // 服务器标志位: 00000000
            0x4F, 0x4B, 0x21,              // 响应内容: "OK!"
            0x00, 0x01, 0x00               // 其他数据
        };
        
        testMessage.setContent(content);
        
        System.out.println("=== 测试在线指令回复包解析 ===");
        System.out.println("原始数据包: 78 78 0F 15 07 00 00 00 00 4F 4B 21 00 01 00 A1 89 53 0D 0A");
        System.out.println("Content部分: " + bytesToHexString(content));
        
        // 执行解析
        commandResponseHandler.handleCommandResponse(testMessage, testImei);
        
        // 验证解析结果
        Object response = commandResponseHandler.getLatestCommandResponse(testImei);
        System.out.println("解析结果: " + response);
        
        System.out.println("\n=== 期望的解析结果 ===");
        System.out.println("指令长度: 7");
        System.out.println("服务器标志位: 00000000");
        System.out.println("响应内容: OK!");
        System.out.println("状态: SUCCESS");
        System.out.println("序列号: 161");
    }

    @Test
    void testParseMultipleResponseFormats() {
        System.out.println("\n=== 测试多种响应格式 ===");
        
        // 测试1: 标准OK响应
        testMessage.setContent(new byte[]{0x07, 0x00, 0x00, 0x00, 0x00, 0x4F, 0x4B, 0x21});
        commandResponseHandler.handleCommandResponse(testMessage, testImei + "_1");
        
        // 测试2: ERROR响应
        testMessage.setContent(new byte[]{0x08, 0x00, 0x00, 0x00, 0x00, 0x45, 0x52, 0x52, 0x4F, 0x52});
        commandResponseHandler.handleCommandResponse(testMessage, testImei + "_2");
        
        // 测试3: 自定义响应
        testMessage.setContent(new byte[]{0x06, 0x00, 0x00, 0x00, 0x00, 0x44, 0x4F, 0x4E, 0x45});
        commandResponseHandler.handleCommandResponse(testMessage, testImei + "_3");
        
        System.out.println("多种格式测试完成");
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b & 0xFF));
        }
        return sb.toString().trim();
    }
}
