package com.yunqu.park.iot.utils;

import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;

/**
 * CRC-ITU校验算法实现 - 基于协议文档提供的C语言代码
 *
 * <AUTHOR>
 */
@Slf4j
public class CrcUtils {
    
    private static final int[] CRC_TAB_16 = {
        0x0000, 0x1189, 0x2312, 0x329B, 0x4624, 0x57AD, 0x6536, 0x74BF,
        0x8C48, 0x9DC1, 0xAF5A, 0xBED3, 0xCA6C, 0xDBE5, 0xE97E, 0xF8F7,
        0x1081, 0x0108, 0x3393, 0x221A, 0x56A5, 0x472C, 0x75B7, 0x643E,
        0x9CC9, 0x8D40, 0xBFDB, 0xAE52, 0xDAED, 0xCB64, 0xF9FF, 0xE876,
        0x2102, 0x308B, 0x0210, 0x1399, 0x6726, 0x76AF, 0x4434, 0x55BD,
        0xAD4A, 0xBCC3, 0x8E58, 0x9FD1, 0xEB6E, 0xFAE7, 0xC87C, 0xD9F5,
        0x3183, 0x200A, 0x1291, 0x0318, 0x77A7, 0x662E, 0x54B5, 0x453C,
        0xBDCB, 0xAC42, 0x9ED9, 0x8F50, 0xFBEF, 0xEA66, 0xD8FD, 0xC974,
        0x4204, 0x538D, 0x6116, 0x709F, 0x0420, 0x15A9, 0x2732, 0x36BB,
        0xCE4C, 0xDFC5, 0xED5E, 0xFCD7, 0x8868, 0x99E1, 0xAB7A, 0xBAF3,
        0x5285, 0x430C, 0x7197, 0x601E, 0x14A1, 0x0528, 0x37B3, 0x263A,
        0xDECD, 0xCF44, 0xFDDF, 0xEC56, 0x98E9, 0x8960, 0xBBFB, 0xAA72,
        0x6306, 0x728F, 0x4014, 0x519D, 0x2522, 0x34AB, 0x0630, 0x17B9,
        0xEF4E, 0xFEC7, 0xCC5C, 0xDDD5, 0xA96A, 0xB8E3, 0x8A78, 0x9BF1,
        0x7387, 0x620E, 0x5095, 0x411C, 0x35A3, 0x242A, 0x16B1, 0x0738,
        0xFFCF, 0xEE46, 0xDCDD, 0xCD54, 0xB9EB, 0xA862, 0x9AF9, 0x8B70,
        0x8408, 0x9581, 0xA71A, 0xB693, 0xC22C, 0xD3A5, 0xE13E, 0xF0B7,
        0x0840, 0x19C9, 0x2B52, 0x3ADB, 0x4E64, 0x5FED, 0x6D76, 0x7CFF,
        0x9489, 0x8500, 0xB79B, 0xA612, 0xD2AD, 0xC324, 0xF1BF, 0xE036,
        0x18C1, 0x0948, 0x3BD3, 0x2A5A, 0x5EE5, 0x4F6C, 0x7DF7, 0x6C7E,
        0xA50A, 0xB483, 0x8618, 0x9791, 0xE32E, 0xF2A7, 0xC03C, 0xD1B5,
        0x2942, 0x38CB, 0x0A50, 0x1BD9, 0x6F66, 0x7EEF, 0x4C74, 0x5DFD,
        0xB58B, 0xA402, 0x9699, 0x8710, 0xF3AF, 0xE226, 0xD0BD, 0xC134,
        0x39C3, 0x284A, 0x1AD1, 0x0B58, 0x7FE7, 0x6E6E, 0x5CF5, 0x4D7C,
        0xC60C, 0xD785, 0xE51E, 0xF497, 0x8028, 0x91A1, 0xA33A, 0xB2B3,
        0x4A44, 0x5BCD, 0x6956, 0x78DF, 0x0C60, 0x1DE9, 0x2F72, 0x3EFB,
        0xD68D, 0xC704, 0xF59F, 0xE416, 0x90A9, 0x8120, 0xB3BB, 0xA232,
        0x5AC5, 0x4B4C, 0x79D7, 0x685E, 0x1CE1, 0x0D68, 0x3FF3, 0x2E7A,
        0xE70E, 0xF687, 0xC41C, 0xD595, 0xA12A, 0xB0A3, 0x8238, 0x93B1,
        0x6B46, 0x7ACF, 0x4854, 0x59DD, 0x2D62, 0x3CEB, 0x0E70, 0x1FF9,
        0xF78F, 0xE606, 0xD49D, 0xC514, 0xB1AB, 0xA022, 0x92B9, 0x8330,
        0x7BC7, 0x6A4E, 0x58D5, 0x495C, 0x3DE3, 0x2C6A, 0x1EF1, 0x0F78
    };
    
    /**
     * 计算给定数据的16位CRC-ITU校验值
     * @param data 待校验的数据
     * @return CRC校验值
     */
    public static int calculateCrcItu(byte[] data) {
        int fcs = 0xFFFF; // 初始化
        
        for (byte b : data) {
            fcs = (fcs >> 8) ^ CRC_TAB_16[(fcs ^ (b & 0xFF)) & 0xFF];
        }
        
        return (~fcs) & 0xFFFF; // 取反并确保16位
    }
    
    /**
     * 验证CRC校验值
     * @param data 包含CRC的完整数据包(不含起始位和停止位)
     * @return 校验是否通过
     */
    public static boolean validateCrc(byte[] data) {
        if (data.length < 4) {
            log.debug("CRC validation failed: data too short, length={}", data.length);
            return false;
        }

        try {
            // 分离数据和CRC
            byte[] dataOnly = Arrays.copyOf(data, data.length - 2);

            // GT06协议：CRC是小端序（低字节在前）
            int receivedCrc = (data[data.length - 1] & 0xFF) << 8 |
                             (data[data.length - 2] & 0xFF);

            // 计算CRC并比较
            int calculatedCrc = calculateCrcItu(dataOnly);

            boolean isValid = calculatedCrc == receivedCrc;

            if (!isValid) {
                log.warn("CRC validation failed: calculated=0x{}, received=0x{}, dataLength={}, data={}",
                        String.format("%04X", calculatedCrc),
                        String.format("%04X", receivedCrc),
                        data.length,
                        bytesToHexString(data));

                // 尝试大端序
                int receivedCrcBigEndian = ((data[data.length - 2] & 0xFF) << 8) |
                                          (data[data.length - 1] & 0xFF);
                boolean isValidBigEndian = calculatedCrc == receivedCrcBigEndian;

                if (isValidBigEndian) {
                    log.info("CRC validation passed with big-endian: calculated=0x{}, received=0x{}",
                            String.format("%04X", calculatedCrc),
                            String.format("%04X", receivedCrcBigEndian));
                    return true;
                }

                log.debug("CRC validation also failed with big-endian: calculated=0x{}, received=0x{}",
                         String.format("%04X", calculatedCrc),
                         String.format("%04X", receivedCrcBigEndian));
            } else {
                log.debug("CRC validation passed: calculated=0x{}, received=0x{}",
                         String.format("%04X", calculatedCrc),
                         String.format("%04X", receivedCrc));
            }

            return isValid;

        } catch (Exception e) {
            log.error("CRC validation error: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 为数据添加CRC校验
     * @param data 原始数据
     * @return 添加CRC后的数据
     */
    public static byte[] addCrc(byte[] data) {
        int crc = calculateCrcItu(data);
        byte[] result = new byte[data.length + 2];
        System.arraycopy(data, 0, result, 0, data.length);

        // GT06协议使用小端序（低字节在前）
        result[data.length] = (byte) (crc & 0xFF);
        result[data.length + 1] = (byte) ((crc >> 8) & 0xFF);
        return result;
    }

    /**
     * 从十六进制字符串计算CRC（兼容concox-master）
     * @param hexData 十六进制字符串
     * @return CRC值
     */
    public static int calculateCrcItuFromHexString(String hexData) {
        if (hexData == null || hexData.trim().isEmpty()) {
            throw new IllegalArgumentException("Hex data cannot be null or empty");
        }

        // 移除空格和转换为小写
        hexData = hexData.replaceAll("\\s+", "").toLowerCase();

        // 确保字符串长度为偶数
        if (hexData.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex string length must be even: " + hexData);
        }

        int fcs = 0xFFFF;

        for (int i = 0; i < hexData.length(); i += 2) {
            try {
                int byteValue = Integer.parseInt(hexData.substring(i, i + 2), 16);
                fcs = (fcs >> 8) ^ CRC_TAB_16[(fcs ^ byteValue) & 0xFF];
            } catch (NumberFormatException e) {
                log.error("Invalid hex character in string: {} at position {}", hexData, i);
                throw new IllegalArgumentException("Invalid hex string: " + hexData, e);
            }
        }

        return (~fcs) & 0xFFFF;
    }

    /**
     * 验证concox格式的CRC
     * @param hexPacket 完整的十六进制数据包（不含起始和结束标志）
     * @return 是否校验通过
     */
    public static boolean validateConcoxCrc(String hexPacket) {
        if (hexPacket == null || hexPacket.length() < 8) { // 至少需要4字节数据 + 2字节CRC
            log.debug("Concox CRC validation failed: packet too short, length={}",
                     hexPacket != null ? hexPacket.length() : 0);
            return false;
        }

        try {
            // 移除空格
            hexPacket = hexPacket.replaceAll("\\s+", "").toLowerCase();

            // 分离数据和CRC
            String dataHex = hexPacket.substring(0, hexPacket.length() - 4);
            String crcHex = hexPacket.substring(hexPacket.length() - 4);

            // 计算CRC
            int calculatedCrc = calculateCrcItuFromHexString(dataHex);
            int receivedCrc = Integer.parseInt(crcHex, 16);

            boolean isValid = calculatedCrc == receivedCrc;

            if (!isValid) {
                log.warn("Concox CRC validation failed: calculated=0x{}, received=0x{}, data={}",
                        String.format("%04X", calculatedCrc),
                        String.format("%04X", receivedCrc),
                        dataHex);
            } else {
                log.debug("Concox CRC validation passed: crc=0x{}", String.format("%04X", calculatedCrc));
            }

            return isValid;

        } catch (Exception e) {
            log.error("Error validating concox CRC: packet={}, error={}", hexPacket, e.getMessage());
            return false;
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

    /**
     * 将字节数组转换为十六进制字符串（无空格）
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHexStringCompact(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b & 0xFF));
        }
        return sb.toString();
    }

    /**
     * 十六进制字符串转字节数组
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.trim().isEmpty()) {
            return new byte[0];
        }

        // 移除空格和转换为小写
        hexString = hexString.replaceAll("\\s+", "").toLowerCase();

        if (hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex string length must be even: " + hexString);
        }

        int len = hexString.length();
        byte[] data = new byte[len / 2];

        for (int i = 0; i < len; i += 2) {
            try {
                data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                                     + Character.digit(hexString.charAt(i + 1), 16));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid hex character in string: " + hexString + " at position " + i, e);
            }
        }

        return data;
    }

    /**
     * 验证CRC校验值（宽松模式）
     * 尝试多种CRC计算方式和字节序
     * @param data 包含CRC的完整数据包(不含起始位和停止位)
     * @return 校验是否通过
     */
    public static boolean validateCrcLenient(byte[] data) {
        if (data.length < 4) {
            log.debug("CRC lenient validation failed: data too short, length={}", data.length);
            return false;
        }

        try {
            // 分离数据和CRC
            byte[] dataOnly = Arrays.copyOf(data, data.length - 2);

            // 尝试小端序
            int receivedCrcLittleEndian = (data[data.length - 1] & 0xFF) << 8 |
                                         (data[data.length - 2] & 0xFF);

            // 尝试大端序
            int receivedCrcBigEndian = ((data[data.length - 2] & 0xFF) << 8) |
                                      (data[data.length - 1] & 0xFF);

            // 计算CRC
            int calculatedCrc = calculateCrcItu(dataOnly);

            // 检查小端序
            if (calculatedCrc == receivedCrcLittleEndian) {
                log.debug("CRC lenient validation passed (little-endian): calculated=0x{}, received=0x{}",
                         String.format("%04X", calculatedCrc),
                         String.format("%04X", receivedCrcLittleEndian));
                return true;
            }

            // 检查大端序
            if (calculatedCrc == receivedCrcBigEndian) {
                log.debug("CRC lenient validation passed (big-endian): calculated=0x{}, received=0x{}",
                         String.format("%04X", calculatedCrc),
                         String.format("%04X", receivedCrcBigEndian));
                return true;
            }

            // 尝试不同的数据范围（可能包含序列号）
            if (data.length >= 6) {
                byte[] dataWithoutSeq = Arrays.copyOf(data, data.length - 4); // 去掉序列号和CRC
                int calculatedCrcWithoutSeq = calculateCrcItu(dataWithoutSeq);

                if (calculatedCrcWithoutSeq == receivedCrcLittleEndian ||
                    calculatedCrcWithoutSeq == receivedCrcBigEndian) {
                    log.debug("CRC lenient validation passed (without sequence): calculated=0x{}",
                             String.format("%04X", calculatedCrcWithoutSeq));
                    return true;
                }
            }

            log.warn("CRC lenient validation failed: calculated=0x{}, receivedLE=0x{}, receivedBE=0x{}, data={}",
                    String.format("%04X", calculatedCrc),
                    String.format("%04X", receivedCrcLittleEndian),
                    String.format("%04X", receivedCrcBigEndian),
                    bytesToHexString(data));

            return false;

        } catch (Exception e) {
            log.error("CRC lenient validation error: {}", e.getMessage(), e);
            return false;
        }
    }
}
