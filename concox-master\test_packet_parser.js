const parser = require('./tcp/parser/index');

// 原始数据包：78 78 0F 15 07 00 00 00 00 4F 4B 21 00 01 00 A1 89 53 0D 0A
const hexPacket = "78780F1507000000004F4B210001000A189530D0A";

console.log("=== 使用concox-master解析GT06协议数据包 ===");
console.log("原始数据包:", "78 78 0F 15 07 00 00 00 00 4F 4B 21 00 01 00 A1 89 53 0D 0A");
console.log("格式化后:", hexPacket);

try {
    const result = parser(hexPacket);
    console.log("\n=== 解析结果 ===");
    if (result) {
        console.log(JSON.stringify(result, null, 2));
    } else {
        console.log("解析失败：返回null");
        console.log("可能的原因：");
        console.log("1. 数据包格式不符合concox-master的期望");
        console.log("2. 协议号0x15未在formatter.js中定义");
        console.log("3. 起始位或结束位验证失败");
    }
} catch (error) {
    console.error("解析过程中发生错误:", error.message);
}

// 让我们手动验证数据包格式
console.log("\n=== 数据包格式验证 ===");
const hexData = hexPacket.toLowerCase();
console.log("起始位检查:", hexData.slice(0, 4), "期望: 7878 或 7979");
console.log("结束位检查:", hexData.slice(-4), "期望: 0d0a");
console.log("协议号:", hexData.slice(6, 8), "(0x15)");

// 尝试修复数据包格式 - 正确的完整数据包
const correctedPacket = "78780f1507000000004f4b210001000a189530d0a";
console.log("\n=== 尝试修复后的数据包 ===");
console.log("修复后的数据包:", correctedPacket);

try {
    const correctedResult = parser(correctedPacket);
    console.log("修复后解析结果:");
    if (correctedResult) {
        console.log(JSON.stringify(correctedResult, null, 2));
    } else {
        console.log("修复后仍然解析失败");
    }
} catch (error) {
    console.error("修复后解析错误:", error.message);
}

// 分析协议号0x15在concox-master中的处理
console.log("\n=== 协议号0x15分析 ===");
console.log("协议号0x15 (十进制21) 在concox-master中的处理:");
console.log("- formatter.js中没有专门的case '15'处理逻辑");
console.log("- 可能会被归类为未知协议，返回基本的input/output结构");
console.log("- 建议在formatter.js中添加对协议号0x15的支持");
