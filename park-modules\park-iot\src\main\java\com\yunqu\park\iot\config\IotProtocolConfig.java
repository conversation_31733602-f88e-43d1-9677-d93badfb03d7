package com.yunqu.park.iot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * IoT协议配置类
 *
 * <p>用于配置IoT协议的兼容性模式和相关参数，支持多种协议格式以确保与不同设备的兼容性。</p>
 *
 * <h3>配置项说明：</h3>
 * <ul>
 *   <li><strong>compatibilityMode</strong>：协议兼容性模式</li>
 *   <li><strong>crc</strong>：CRC校验相关配置</li>
 *   <li><strong>serialNumber</strong>：序列号生成配置</li>
 * </ul>
 *
 * <h3>兼容性模式：</h3>
 * <ul>
 *   <li><strong>STANDARD</strong>：park-iot原始格式（修复CRC后）</li>
 *   <li><strong>CONCOX</strong>：concox-master兼容格式（推荐）</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-08-07
 */
@Data
@Component
@ConfigurationProperties(prefix = "iot.protocol")
public class IotProtocolConfig {

    /**
     * 协议兼容性模式，默认使用CONCOX模式确保最大兼容性
     */
    private CompatibilityMode compatibilityMode = CompatibilityMode.CONCOX;

    /**
     * 是否启用严格的CRC校验
     * 开发环境可以设置为false，生产环境建议设置为true
     */
    private boolean strictCrcValidation = false;

    /**
     * 是否启用协议调试日志
     */
    private boolean debugEnabled = true;

    /**
     * 最大数据包大小（字节）
     */
    private int maxPacketSize = 1024;

    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeout = 300;

    /**
     * 心跳间隔（秒）
     */
    private int heartbeatInterval = 60;

    /**
     * 是否自动注册新设备
     */
    private boolean autoRegisterDevice = true;

    /**
     * 支持的协议版本
     */
    private String[] supportedVersions = {"GT06", "GT02", "GT09"};

    /**
     * 序列号配置
     */
    private SerialNumberConfig serialNumber = new SerialNumberConfig();

    /**
     * CRC校验配置
     */
    private CrcConfig crc = new CrcConfig();

    /**
     * 序列号配置
     */
    @Data
    public static class SerialNumberConfig {
        /**
         * 序列号起始值
         */
        private int startValue = 1;

        /**
         * 序列号最大值
         */
        private int maxValue = 65535;

        /**
         * 是否自动递增
         */
        private boolean autoIncrement = true;
    }

    @Data
    public static class CrcConfig {
        /**
         * 是否启用CRC校验
         */
        private boolean enabled = true;

        /**
         * CRC算法类型
         */
        private String algorithm = "crc-itu";

        /**
         * 是否使用宽松模式（尝试多种字节序和数据范围）
         */
        private boolean lenientMode = true;

        /**
         * CRC校验失败时是否继续处理
         */
        private boolean continueOnFailure = true;

        /**
         * 是否记录CRC校验详细日志
         */
        private boolean verboseLogging = true;
    }

    /**
     * 协议兼容性模式枚举
     */
    public enum CompatibilityMode {
        /**
         * 标准模式：park-iot原始格式（修复CRC后）
         * 适用于：park-iot专用设备
         */
        STANDARD("Standard park-iot format with fixed CRC"),

        /**
         * Concox兼容模式：concox-master兼容格式
         * 适用于：标准GT06协议设备，推荐使用
         */
        CONCOX("Concox-master compatible format");

        private final String description;

        CompatibilityMode(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取当前配置的描述信息
     * @return 配置描述
     */
    public String getConfigDescription() {
        return String.format("IoT Protocol Config: mode=%s, crc=%s, debug=%s",
                compatibilityMode.name(),
                crc.enabled ? "enabled" : "disabled",
                debugEnabled ? "enabled" : "disabled");
    }

    /**
     * 验证配置的有效性
     * @return 配置是否有效
     */
    public boolean isValid() {
        return compatibilityMode != null
                && crc != null
                && serialNumber != null
                && serialNumber.startValue >= 0
                && serialNumber.maxValue > serialNumber.startValue
                && connectionTimeout > 0
                && heartbeatInterval > 0
                && maxPacketSize > 0;
    }
}
