/**
 * IMEI解析修复验证程序
 */
public class ImeiFixVerification {
    
    public static void main(String[] args) {
        System.out.println("=== IMEI解析修复验证 ===\n");
        
        // 测试数据：登录包IMEI部分
        byte[] imeiData = {0x03, 0x53, (byte)0x99, 0x47, 0x14, 0x29, 0x02, 0x66};
        
        System.out.println("原始IMEI数据: " + bytesToHexString(imeiData));
        System.out.println();
        
        // 修复前的解析逻辑
        String oldResult = parseImeiOldWay(imeiData);
        System.out.println("修复前解析结果: " + oldResult);
        
        // 修复后的解析逻辑
        String newResult = parseImeiNewWay(imeiData);
        System.out.println("修复后解析结果: " + newResult);
        
        System.out.println();
        System.out.println("期望的正确IMEI: 353994714290266");
        System.out.println();
        
        // 验证结果
        if ("353994714290266".equals(newResult)) {
            System.out.println("✅ 修复成功！IMEI解析正确");
        } else {
            System.out.println("❌ 修复失败！IMEI解析仍有问题");
        }
        
        // 详细对比
        System.out.println("\n=== 详细解析对比 ===");
        compareParsingMethods(imeiData);
        
        // 测试其他IMEI
        System.out.println("\n=== 其他IMEI测试 ===");
        testOtherImeis();
    }
    
    /**
     * 修复前的解析方法 (错误的)
     */
    private static String parseImeiOldWay(byte[] content) {
        StringBuilder imei = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            int high = (content[i] >> 4) & 0x0F;
            int low = content[i] & 0x0F;
            imei.append(high).append(low);
        }
        String result = imei.toString();
        return result.length() > 15 ? result.substring(0, 15) : result;
    }
    
    /**
     * 修复后的解析方法 (正确的)
     */
    private static String parseImeiNewWay(byte[] content) {
        if (content.length < 8) {
            return null;
        }
        
        StringBuilder imei = new StringBuilder();
        
        // 第一个字节只取低4位
        imei.append(content[0] & 0x0F);
        
        // 后续7个字节正常解析
        for (int i = 1; i < 8; i++) {
            int high = (content[i] >> 4) & 0x0F;
            int low = content[i] & 0x0F;
            imei.append(high).append(low);
        }
        
        return imei.toString();
    }
    
    /**
     * 详细对比两种解析方法
     */
    private static void compareParsingMethods(byte[] data) {
        System.out.println("字节位置 | 十六进制 | 修复前解析 | 修复后解析 | 说明");
        System.out.println("---------|----------|------------|------------|------");
        
        StringBuilder oldWay = new StringBuilder();
        StringBuilder newWay = new StringBuilder();
        
        for (int i = 0; i < 8; i++) {
            int high = (data[i] >> 4) & 0x0F;
            int low = data[i] & 0x0F;
            
            String oldPart, newPart, explanation;
            
            if (i == 0) {
                oldPart = "" + high + low;
                newPart = "" + low;
                explanation = "第1字节：修复前取高低4位，修复后只取低4位";
                oldWay.append(high).append(low);
                newWay.append(low);
            } else {
                oldPart = "" + high + low;
                newPart = "" + high + low;
                explanation = "第" + (i+1) + "字节：正常解析高低4位";
                oldWay.append(high).append(low);
                newWay.append(high).append(low);
            }
            
            System.out.printf("   %d     |   %02X     |     %s     |     %s     | %s%n", 
                            i+1, data[i] & 0xFF, oldPart, newPart, explanation);
        }
        
        System.out.println();
        System.out.println("完整结果对比:");
        System.out.println("修复前: " + oldWay.toString() + " → 截取15位 → " + 
                          (oldWay.length() > 15 ? oldWay.substring(0, 15) : oldWay.toString()));
        System.out.println("修复后: " + newWay.toString() + " (15位)");
    }
    
    /**
     * 测试其他IMEI示例
     */
    private static void testOtherImeis() {
        // 测试用例
        byte[][] testCases = {
            {0x08, 0x64, 0x17, 0x59, 0x26, 0x38, 0x40, 0x12}, // 864175926384012
            {0x01, 0x23, 0x45, 0x67, (byte)0x89, 0x01, 0x23, 0x45}, // 123456789012345
            {0x09, (byte)0x87, 0x65, 0x43, 0x21, (byte)0x98, 0x76, 0x54}  // 987654321987654
        };
        
        String[] expectedResults = {
            "864175926384012",
            "123456789012345", 
            "987654321987654"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.println("测试用例 " + (i+1) + ":");
            System.out.println("  原始数据: " + bytesToHexString(testCases[i]));
            
            String oldResult = parseImeiOldWay(testCases[i]);
            String newResult = parseImeiNewWay(testCases[i]);
            
            System.out.println("  修复前: " + oldResult);
            System.out.println("  修复后: " + newResult);
            System.out.println("  期望值: " + expectedResults[i]);
            System.out.println("  结果: " + (expectedResults[i].equals(newResult) ? "✅ 正确" : "❌ 错误"));
            System.out.println();
        }
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            if (i > 0) sb.append(" ");
            sb.append(String.format("%02X", bytes[i] & 0xFF));
        }
        return sb.toString();
    }
}
