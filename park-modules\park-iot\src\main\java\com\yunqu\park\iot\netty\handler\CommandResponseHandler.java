package com.yunqu.park.iot.netty.handler;

import com.yunqu.park.common.redis.utils.RedisUtils;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备指令响应处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandResponseHandler {

    private static final String COMMAND_RESPONSE_PREFIX = "iot:command:response:";

    /**
     * 处理设备指令响应
     * @param message 响应消息
     * @param imei 设备IMEI号
     */
    public void handleCommandResponse(IotMessage message, String imei) {
        try {
            if (message.getProtocol() == IotConstants.GT06Protocol.PROTOCOL_STRING_INFO) {
                // 解析指令响应
                parseTerminalResponse(message, imei);
            } else {
                log.debug("Received non-command response from device: {}", imei);
            }
        } catch (Exception e) {
            log.error("Failed to handle command response from device {}: {}", imei, e.getMessage(), e);
        }
    }

    /**
     * 解析终端指令响应
     * @param message 响应消息
     * @param imei 设备IMEI号
     */
    private void parseTerminalResponse(IotMessage message, String imei) {
        try {
            byte[] content = message.getContent();
            if (content == null || content.length < 7) {
                log.warn("Invalid terminal response content from device: {}, content length: {}",
                        imei, content != null ? content.length : 0);
                return;
            }

            // 按照在线指令回复包格式解析
            parseOnlineCommandResponse(content, message, imei);

        } catch (Exception e) {
            log.error("Failed to parse terminal response from device {}: {}", imei, e.getMessage(), e);
        }
    }

    /**
     * 解析在线指令回复包 (协议号0x15)
     * 数据结构: 指令长度(1) + 服务器标志位(4) + 响应内容(N) + 其他数据
     * @param content 消息内容
     * @param message 完整消息对象
     * @param imei 设备IMEI号
     */
    private void parseOnlineCommandResponse(byte[] content, IotMessage message, String imei) {
        try {
            // 解析指令长度
            int commandLength = content[0] & 0xFF;
            log.debug("Parsing online command response: commandLength={}, contentLength={}",
                     commandLength, content.length);

            // 解析服务器标志位 (4字节)
            byte[] serverFlag = new byte[4];
            System.arraycopy(content, 1, serverFlag, 0, 4);

            // 计算响应内容的实际长度和位置
            int responseContentStart = 5; // 跳过指令长度(1) + 服务器标志位(4)
            int maxResponseLength = Math.min(commandLength - 4, content.length - responseContentStart);

            String responseContent = "";
            byte[] responseBytes = null;

            if (maxResponseLength > 0 && responseContentStart + maxResponseLength <= content.length) {
                responseBytes = new byte[maxResponseLength];
                System.arraycopy(content, responseContentStart, responseBytes, 0, maxResponseLength);

                // 尝试解析为ASCII文本
                responseContent = parseResponseContent(responseBytes);
            }

            // 解析状态 - 根据响应内容判断
            String status = determineResponseStatus(responseContent, responseBytes);

            // 缓存响应结果
            String cacheKey = COMMAND_RESPONSE_PREFIX + imei;
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("commandLength", commandLength);
            responseData.put("serverFlag", bytesToHexString(serverFlag));
            responseData.put("responseContent", responseContent);
            responseData.put("responseBytes", responseBytes);
            responseData.put("status", status);
            responseData.put("timestamp", System.currentTimeMillis());
            responseData.put("sequenceNumber", message.getSequenceNumber());

            log.info("[RESPONSE] 数据响应: Protocol={}, SequenceNumber={}, ResponseContent={}", message.getProtocolName(), message.getSequenceNumber(), responseContent);

            // 如果有剩余数据
            if (content.length > responseContentStart + maxResponseLength) {
                int remainingStart = responseContentStart + maxResponseLength;
                byte[] remainingData = new byte[content.length - remainingStart];
                System.arraycopy(content, remainingStart, remainingData, 0, remainingData.length);
                responseData.put("remainingData", remainingData);
            }

            RedisUtils.setCacheObject(cacheKey, responseData, Duration.ofMinutes(10));

            log.info("Online command response received from device {}: content='{}', status={}, commandLength={}, sequenceNumber={}",
                    imei, responseContent, status, commandLength, message.getSequenceNumber());

        } catch (Exception e) {
            log.error("Failed to parse online command response from device {}: {}", imei, e.getMessage(), e);
        }
    }

    /**
     * 解析响应内容为可读文本
     * @param responseBytes 响应字节数组
     * @return 解析后的文本内容
     */
    private String parseResponseContent(byte[] responseBytes) {
        if (responseBytes == null || responseBytes.length == 0) {
            return "";
        }

        try {
            // 尝试解析为ASCII文本
            StringBuilder content = new StringBuilder();
            for (byte b : responseBytes) {
                if (b >= 0x20 && b <= 0x7E) { // 可打印ASCII字符
                    content.append((char) b);
                } else if (b == 0x21) { // 感叹号
                    content.append('!');
                } else {
                    // 对于非ASCII字符，显示十六进制
                    content.append(String.format("[0x%02X]", b & 0xFF));
                }
            }
            return content.toString();
        } catch (Exception e) {
            // 如果解析失败，返回十六进制表示
            return bytesToHexString(responseBytes);
        }
    }

    /**
     * 根据响应内容确定状态
     * @param responseContent 响应文本内容
     * @param responseBytes 原始响应字节
     * @return 状态描述
     */
    private String determineResponseStatus(String responseContent, byte[] responseBytes) {
        if (responseContent == null || responseContent.isEmpty()) {
            return "NO_CONTENT";
        }

        // 检查常见的成功响应
        String upperContent = responseContent.toUpperCase();
        if (upperContent.contains("OK")) {
            return "SUCCESS";
        } else if (upperContent.contains("ERROR") || upperContent.contains("FAIL")) {
            return "FAILED";
        } else if (upperContent.contains("INVALID")) {
            return "INVALID_COMMAND";
        } else {
            return "CUSTOM_RESPONSE";
        }
    }

    /**
     * 字节数组转十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b & 0xFF));
        }
        return sb.toString();
    }

    /**
     * 解析响应状态码 (保留原方法用于兼容性)
     * @param statusCode 状态码
     * @return 状态描述
     */
    private String parseResponseStatus(byte statusCode) {
        return switch (statusCode & 0xFF) {
            case 0x00 -> "SUCCESS";
            case 0x01 -> "FAILED";
            case 0x02 -> "INVALID_COMMAND";
            case 0x03 -> "UNSUPPORTED";
            case 0x04 -> "PARAMETER_ERROR";
            case 0x05 -> "DEVICE_BUSY";
            default -> "UNKNOWN(" + String.format("0x%02X", statusCode & 0xFF) + ")";
        };
    }

    /**
     * 获取设备最新的指令响应
     * @param imei 设备IMEI号
     * @return 响应数据
     */
    public Object getLatestCommandResponse(String imei) {
        try {
            String cacheKey = COMMAND_RESPONSE_PREFIX + imei;
            return RedisUtils.getCacheObject(cacheKey);
        } catch (Exception e) {
            log.error("Failed to get command response for device {}: {}", imei, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清除设备指令响应缓存
     * @param imei 设备IMEI号
     */
    public void clearCommandResponse(String imei) {
        try {
            String cacheKey = COMMAND_RESPONSE_PREFIX + imei;
            RedisUtils.deleteObject(cacheKey);
        } catch (Exception e) {
            log.error("Failed to clear command response for device {}: {}", imei, e.getMessage(), e);
        }
    }
}
