{"name": "es5-ext", "version": "0.10.53", "description": "ECMAScript extensions and shims", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["ecmascript", "ecmascript5", "ecmascript6", "es5", "es6", "extensions", "ext", "addons", "extras", "harmony", "javascript", "polyfill", "shim", "util", "utils", "utilities"], "repository": "medikoo/es5-ext", "dependencies": {"es6-iterator": "~2.0.3", "es6-symbol": "~3.1.3", "next-tick": "~1.0.0"}, "devDependencies": {"eslint": "^6.6.0", "eslint-config-medikoo": "^2.7.0", "git-list-updated": "^1.2.1", "husky": "^3.1.0", "lint-staged": "^9.4.3", "plain-promise": "^0.1.1", "prettier-elastic": "^1.18.2", "tad": "^3.0.1"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "eslintConfig": {"extends": "medikoo/es5", "root": true, "rules": {"no-extend-native": "off"}, "overrides": [{"files": "global.js", "globals": {"__global__": true, "globalThis": true, "self": true, "window": true}, "rules": {"strict": "off"}}]}, "prettier": {"printWidth": 100, "tabWidth": 4, "overrides": [{"files": "*.md", "options": {"tabWidth": 2}}]}, "scripts": {"lint": "eslint --ignore-path=.gitignore .", "lint-updated": "pipe-git-updated --ext=js -- eslint --ignore-pattern '!*'", "prettier-check-updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore '**/*.{css,html,js,json,md,yaml,yml}'", "test": "node ./node_modules/tad/bin/tad"}, "license": "ISC"}