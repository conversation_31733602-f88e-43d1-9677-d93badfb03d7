const parser = require('./tcp/parser/index');

console.log("=== concox-master GT06协议数据包详细解析报告 ===\n");

// 原始数据包
const originalPacket = "78 78 0F 15 07 00 00 00 00 4F 4B 21 00 01 00 A1 89 53 0D 0A";
const hexPacket = "78780f1507000000004f4b210001000a189530d0a";

console.log("📦 原始数据包:");
console.log("   十六进制:", originalPacket);
console.log("   格式化后:", hexPacket.toUpperCase());
console.log();

// 解析数据包
const result = parser(hexPacket);

if (result && result.length > 0) {
    const parsed = result[0];
    
    console.log("✅ 解析成功!");
    console.log();
    
    console.log("📋 基本信息:");
    console.log(`   协议类型: GT06协议`);
    console.log(`   消息类型: ${parsed.tag} (协议号: 0x${parsed.case})`);
    console.log(`   解析时间: ${parsed.time}`);
    console.log(`   序列号: ${parsed.info_serial_no}`);
    console.log();
    
    console.log("📊 状态信息:");
    console.log(`   状态码: 0x${parsed.status_code.toString(16).padStart(2, '0').toUpperCase()} (${parsed.status_code})`);
    console.log(`   状态描述: ${parsed.status_description}`);
    console.log();
    
    console.log("📝 响应内容:");
    console.log(`   内容类型: ${parsed.response_content.type}`);
    console.log(`   响应文本: "${parsed.response_content.content}"`);
    console.log(`   原始十六进制: ${parsed.response_content.raw_hex.toUpperCase()}`);
    console.log();
    
    console.log("🔄 服务器响应:");
    console.log(`   响应数据包: ${parsed.output.toUpperCase()}`);
    console.log();
    
    console.log("🔍 数据包结构分析:");
    console.log("   起始位: 78 78 (GT06协议标识)");
    console.log("   包长度: 0F (15字节)");
    console.log("   协议号: 15 (字符串信息响应)");
    console.log("   状态码: 07 (自定义成功状态)");
    console.log("   填充字段: 00 00 00 00");
    console.log("   响应内容: 4F 4B (ASCII 'OK')");
    console.log("   扩展信息: 21 00 01 00");
    console.log("   序列号: 00 A1 (161)");
    console.log("   CRC校验: 89 53");
    console.log("   停止位: 0D 0A");
    console.log();
    
    console.log("💡 业务含义:");
    console.log("   这是一个GPS追踪设备对服务器指令的成功响应");
    console.log("   设备确认已成功执行了服务器下发的指令");
    console.log("   状态码0x07表示设备特定的成功状态");
    console.log("   'OK'字符串进一步确认了操作的成功完成");
    console.log();
    
    console.log("🔧 技术细节:");
    console.log("   - 符合GT06协议标准格式");
    console.log("   - 包含完整的协议头部和尾部");
    console.log("   - CRC校验确保数据完整性");
    console.log("   - 序列号用于请求-响应匹配");
    console.log("   - 支持双向通信确认机制");
    
} else {
    console.log("❌ 解析失败");
    console.log("可能的原因:");
    console.log("- 数据包格式不正确");
    console.log("- 协议号未被支持");
    console.log("- CRC校验失败");
}

console.log("\n=== 解析完成 ===");
