//! moment.js locale configuration
//! locale : Kurdish [ku]
//! author : <PERSON><PERSON> : https://github.com/ShahramMebashar

import moment from '../moment';

var symbolMap = {
    '1': '١',
    '2': '٢',
    '3': '٣',
    '4': '٤',
    '5': '٥',
    '6': '٦',
    '7': '٧',
    '8': '٨',
    '9': '٩',
    '0': '٠'
}, numberMap = {
    '١': '1',
    '٢': '2',
    '٣': '3',
    '٤': '4',
    '٥': '5',
    '٦': '6',
    '٧': '7',
    '٨': '8',
    '٩': '9',
    '٠': '0'
},
months = [
    'کانونی دووەم',
    'شوبات',
    'ئازار',
    'نیسان',
    'ئایار',
    'حوزەیران',
    'تەمموز',
    'ئاب',
    'ئەیلوول',
    'تشرینی یەكەم',
    'تشرینی دووەم',
    'كانونی یەکەم'
];


export default moment.define<PERSON><PERSON><PERSON>('ku', {
    months : months,
    monthsShort : months,
    weekdays : 'یه‌كشه‌ممه‌_دووشه‌ممه‌_سێشه‌ممه‌_چوارشه‌ممه‌_پێنجشه‌ممه‌_هه‌ینی_شه‌ممه‌'.split('_'),
    weekdaysShort : 'یه‌كشه‌م_دووشه‌م_سێشه‌م_چوارشه‌م_پێنجشه‌م_هه‌ینی_شه‌ممه‌'.split('_'),
    weekdaysMin : 'ی_د_س_چ_پ_ه_ش'.split('_'),
    weekdaysParseExact : true,
    longDateFormat : {
        LT : 'HH:mm',
        LTS : 'HH:mm:ss',
        L : 'DD/MM/YYYY',
        LL : 'D MMMM YYYY',
        LLL : 'D MMMM YYYY HH:mm',
        LLLL : 'dddd, D MMMM YYYY HH:mm'
    },
    meridiemParse: /ئێواره‌|به‌یانی/,
    isPM: function (input) {
        return /ئێواره‌/.test(input);
    },
    meridiem : function (hour, minute, isLower) {
        if (hour < 12) {
            return 'به‌یانی';
        } else {
            return 'ئێواره‌';
        }
    },
    calendar : {
        sameDay : '[ئه‌مرۆ كاتژمێر] LT',
        nextDay : '[به‌یانی كاتژمێر] LT',
        nextWeek : 'dddd [كاتژمێر] LT',
        lastDay : '[دوێنێ كاتژمێر] LT',
        lastWeek : 'dddd [كاتژمێر] LT',
        sameElse : 'L'
    },
    relativeTime : {
        future : 'له‌ %s',
        past : '%s',
        s : 'چه‌ند چركه‌یه‌ك',
        ss : 'چركه‌ %d',
        m : 'یه‌ك خوله‌ك',
        mm : '%d خوله‌ك',
        h : 'یه‌ك كاتژمێر',
        hh : '%d كاتژمێر',
        d : 'یه‌ك ڕۆژ',
        dd : '%d ڕۆژ',
        M : 'یه‌ك مانگ',
        MM : '%d مانگ',
        y : 'یه‌ك ساڵ',
        yy : '%d ساڵ'
    },
    preparse: function (string) {
        return string.replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {
            return numberMap[match];
        }).replace(/،/g, ',');
    },
    postformat: function (string) {
        return string.replace(/\d/g, function (match) {
            return symbolMap[match];
        }).replace(/,/g, '،');
    },
    week : {
        dow : 6, // Saturday is the first day of the week.
        doy : 12 // The week that contains Jan 12th is the first week of the year.
    }
});
