package com.yunqu.park.iot.netty.handler;

import com.yunqu.park.iot.netty.codec.GT06ProtocolDecoder;
import org.junit.jupiter.api.Test;

/**
 * 定位数据包解析测试
 * 分析数据包: 78 78 1F 12 19 08 06 13 37 1E CE 02 7C 59 33 0C 2B 90 20 00 D5 22 01 CC 00 25 03 00 0B 11 07 82 28 65 0D 0A
 */
public class LocationPacketAnalysisTest {

    @Test
    public void analyzeLocationPacket() {
        // 原始数据包
        String hexPacket = "78781F1219080613371ECE027C59330C2B902000D52201CC0025030000B11078228650D0A";
        byte[] packetBytes = hexStringToByteArray(hexPacket);
        
        System.out.println("=== GPS定位数据包深度解析 ===");
        System.out.println("原始数据包: 78 78 1F 12 19 08 06 13 37 1E CE 02 7C 59 33 0C 2B 90 20 00 D5 22 01 CC 00 25 03 00 0B 11 07 82 28 65 0D 0A");
        System.out.println("数据包长度: " + packetBytes.length + " 字节");
        System.out.println();
        
        // 解析包结构
        analyzePacketStructure(packetBytes);
        
        // 解析GPS数据
        analyzeGpsData(packetBytes);
        
        // 解析LBS数据
        analyzeLbsData(packetBytes);
    }
    
    private void analyzePacketStructure(byte[] packet) {
        System.out.println("📋 数据包结构分析:");
        System.out.println("  起始位: " + String.format("%02X %02X", packet[0], packet[1]) + " (GT06协议标识)");
        System.out.println("  包长度: " + String.format("%02X", packet[2]) + " (" + (packet[2] & 0xFF) + "字节)");
        System.out.println("  协议号: " + String.format("%02X", packet[3]) + " (0x12 - 定位数据包)");
        
        // 提取数据部分 (去掉起始位、长度、停止位)
        int dataLength = packet[2] & 0xFF;
        byte[] dataSection = new byte[dataLength];
        System.arraycopy(packet, 3, dataSection, 0, dataLength);
        
        // 解析序列号和CRC
        int sequenceNumber = ((dataSection[dataLength-4] & 0xFF) << 8) | (dataSection[dataLength-3] & 0xFF);
        int crc = ((dataSection[dataLength-2] & 0xFF) << 8) | (dataSection[dataLength-1] & 0xFF);
        
        System.out.println("  序列号: " + String.format("%02X %02X", dataSection[dataLength-4], dataSection[dataLength-3]) + 
                          " (" + sequenceNumber + ")");
        System.out.println("  CRC校验: " + String.format("%02X %02X", dataSection[dataLength-2], dataSection[dataLength-1]) + 
                          " (" + crc + ")");
        System.out.println("  停止位: " + String.format("%02X %02X", packet[packet.length-2], packet[packet.length-1]) + " (0x0D0A)");
        System.out.println();
    }
    
    private void analyzeGpsData(byte[] packet) {
        System.out.println("🛰️ GPS数据解析:");
        
        // 提取内容部分 (协议号后面，去掉序列号和CRC)
        byte[] content = new byte[27]; // 31 - 4 = 27字节
        System.arraycopy(packet, 4, content, 0, 27);
        
        // 解析日期时间 (BCD码)
        int year = 2000 + bcdToDec(content[0]);
        int month = bcdToDec(content[1]);
        int day = bcdToDec(content[2]);
        int hour = bcdToDec(content[3]);
        int minute = bcdToDec(content[4]);
        int second = bcdToDec(content[5]);
        
        System.out.println("  定位时间: " + String.format("%04d-%02d-%02d %02d:%02d:%02d", 
                          year, month, day, hour, minute, second));
        
        // 解析卫星数量
        int satellites = content[6] & 0x0F;
        System.out.println("  卫星数量: " + satellites + "颗");
        
        // 解析经纬度
        byte[] latBytes = {content[7], content[8], content[9], content[10]};
        byte[] lngBytes = {content[11], content[12], content[13], content[14]};
        
        double rawLatitude = GT06ProtocolDecoder.parseCoordinate(latBytes);
        double rawLongitude = GT06ProtocolDecoder.parseCoordinate(lngBytes);
        
        System.out.println("  原始纬度: " + rawLatitude + "° (需要转换为实际坐标)");
        System.out.println("  原始经度: " + rawLongitude + "° (需要转换为实际坐标)");
        
        // 解析速度和航向
        int speed = content[15] & 0xFF;
        int status = content[16] & 0xFF;
        int course = content[17] & 0xFF;
        
        System.out.println("  速度: " + speed + " km/h");
        System.out.println("  状态: 0x" + String.format("%02X", status));
        System.out.println("  航向: " + course + "° (" + getDirection(course) + ")");
        System.out.println();
    }
    
    private void analyzeLbsData(byte[] packet) {
        System.out.println("📡 LBS基站数据解析:");
        
        // LBS数据从第18字节开始
        byte[] content = new byte[27];
        System.arraycopy(packet, 4, content, 0, 27);
        
        if (content.length > 18) {
            // 解析LBS信息 (如果存在)
            System.out.println("  LBS数据: " + bytesToHexString(content, 18, Math.min(content.length, 26)));
            
            // 可能的MCC/MNC/LAC/CellID解析
            if (content.length >= 26) {
                int mcc = ((content[18] & 0xFF) << 8) | (content[19] & 0xFF);
                int mnc = content[20] & 0xFF;
                int lac = ((content[21] & 0xFF) << 8) | (content[22] & 0xFF);
                int cellId = ((content[23] & 0xFF) << 16) | ((content[24] & 0xFF) << 8) | (content[25] & 0xFF);
                
                System.out.println("  MCC: " + mcc + " (移动国家代码)");
                System.out.println("  MNC: " + mnc + " (移动网络代码)");
                System.out.println("  LAC: " + lac + " (位置区域代码)");
                System.out.println("  Cell ID: " + cellId + " (基站小区ID)");
            }
        } else {
            System.out.println("  无LBS数据");
        }
        System.out.println();
    }
    
    /**
     * BCD码转十进制
     */
    private int bcdToDec(byte bcd) {
        return ((bcd & 0xF0) >> 4) * 10 + (bcd & 0x0F);
    }
    
    /**
     * 根据角度获取方向描述
     */
    private String getDirection(int course) {
        if (course >= 337.5 || course < 22.5) return "北";
        if (course >= 22.5 && course < 67.5) return "东北";
        if (course >= 67.5 && course < 112.5) return "东";
        if (course >= 112.5 && course < 157.5) return "东南";
        if (course >= 157.5 && course < 202.5) return "南";
        if (course >= 202.5 && course < 247.5) return "西南";
        if (course >= 247.5 && course < 292.5) return "西";
        if (course >= 292.5 && course < 337.5) return "西北";
        return "未知";
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHexString(byte[] bytes, int start, int end) {
        StringBuilder sb = new StringBuilder();
        for (int i = start; i < end && i < bytes.length; i++) {
            if (i > start) sb.append(" ");
            sb.append(String.format("%02X", bytes[i] & 0xFF));
        }
        return sb.toString();
    }
    
    /**
     * 十六进制字符串转字节数组
     */
    private byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                                + Character.digit(hex.charAt(i+1), 16));
        }
        return data;
    }
}
