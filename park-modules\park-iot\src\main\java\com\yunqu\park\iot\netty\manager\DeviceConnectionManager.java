package com.yunqu.park.iot.netty.manager;

import com.yunqu.park.iot.constant.IotLogMarkers;
import com.yunqu.park.iot.protocol.ProtocolPacketBuilder;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Iterator;
import java.util.Map;
import java.util.HashMap;

/**
 * 设备连接管理器
 * 用于管理设备连接和发送指令
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeviceConnectionManager {

    private final ProtocolPacketBuilder packetBuilder;

    // 存储IMEI与Channel的映射关系
    private final ConcurrentHashMap<String, Channel> deviceChannels = new ConcurrentHashMap<>();

    // 存储Channel与IMEI的映射关系
    private final ConcurrentHashMap<String, String> channelImeiMap = new ConcurrentHashMap<>();

    /**
     * 阶段1增强：连接数监控和限制
     */
    // 连接数计数器
    private final AtomicInteger connectionCount = new AtomicInteger(0);

    // 最大连接数限制 - 从配置文件读取
    @Value("${iot.connection.max-connections:10000}")
    private int maxConnections;

    // 每IP最大连接数限制
    @Value("${iot.connection.max-connections-per-ip:100}")
    private int maxConnectionsPerIp;

    // IP连接计数器
    private final ConcurrentHashMap<String, AtomicInteger> ipConnectionCount = new ConcurrentHashMap<>();

    /**
     * 注册设备连接
     * 阶段1增强：添加连接数限制和IP限制检查
     * @param imei 设备IMEI号
     * @param channel 网络通道
     * @return 是否注册成功
     */
    public boolean registerDevice(String imei, Channel channel) {
        try {
            String channelId = channel.id().asShortText();
            String remoteAddress = channel.remoteAddress().toString();
            String clientIp = extractClientIp(remoteAddress);

            log.info("[CONNECTION-REGISTER] 🔗 开始注册设备连接: IMEI={}, ChannelId={}, RemoteAddress={}",
                    imei, channelId, remoteAddress);

            // 阶段1增强：检查连接数限制
            if (connectionCount.get() >= maxConnections) {
                log.warn("[CONNECTION-REGISTER] ❌ 连接数已达上限: current={}, max={}, 拒绝连接: IMEI={}",
                        connectionCount.get(), maxConnections, imei);
                return false;
            }

            // 阶段1增强：检查IP连接数限制
            AtomicInteger ipCount = ipConnectionCount.computeIfAbsent(clientIp, k -> new AtomicInteger(0));
            if (ipCount.get() >= maxConnectionsPerIp) {
                log.warn("[CONNECTION-REGISTER] ❌ IP连接数已达上限: IP={}, current={}, max={}, 拒绝连接: IMEI={}",
                        clientIp, ipCount.get(), maxConnectionsPerIp, imei);
                return false;
            }

            // 移除旧的连接（如果存在）
            Channel oldChannel = deviceChannels.get(imei);
            if (oldChannel != null) {
                String oldChannelId = oldChannel.id().asShortText();
                log.warn("[CONNECTION-REGISTER] ⚠️ 发现旧连接，准备替换: IMEI={}, OldChannelId={}", imei, oldChannelId);
                removeDeviceConnection(imei);
            }

            // 注册新连接
            deviceChannels.put(imei, channel);
            channelImeiMap.put(channelId, imei);

            // 阶段1增强：更新连接计数器
            connectionCount.incrementAndGet();
            ipCount.incrementAndGet();

            log.info(IotLogMarkers.IOT_CONNECTION,
                    "[CONNECTION-REGISTER] ✅ 设备连接注册成功: IMEI={}, ChannelId={}, TotalConnections={}, IP={}, IPConnections={}",
                    imei, channelId, connectionCount.get(), clientIp, ipCount.get());

            return true;

        } catch (Exception e) {
            log.error("[CONNECTION-REGISTER] ❌ 设备连接注册失败: IMEI={}, Error={}",
                     imei, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 移除设备连接
     * 阶段1增强：更新连接计数器和IP计数器
     * @param imei 设备IMEI号
     */
    public void removeDeviceConnection(String imei) {
        try {
            log.debug("[CONNECTION-REMOVE] 🔌 开始移除设备连接: IMEI={}", imei);

            Channel oldChannel = deviceChannels.remove(imei);
            if (oldChannel != null) {
                String oldChannelId = oldChannel.id().asShortText();
                String remoteAddress = oldChannel.remoteAddress() != null ? oldChannel.remoteAddress().toString() : "unknown";
                String clientIp = extractClientIp(remoteAddress);

                channelImeiMap.remove(oldChannelId);

                // 阶段1增强：更新连接计数器
                connectionCount.decrementAndGet();

                // 更新IP连接计数器
                AtomicInteger ipCount = ipConnectionCount.get(clientIp);
                if (ipCount != null) {
                    int remaining = ipCount.decrementAndGet();
                    if (remaining <= 0) {
                        ipConnectionCount.remove(clientIp);
                    }
                }

                log.info(IotLogMarkers.IOT_CONNECTION,
                        "[CONNECTION-REMOVE] ✅ 设备连接已移除: IMEI={}, ChannelId={}, RemoteAddress={}, RemainingConnections={}, IP={}",
                        imei, oldChannelId, remoteAddress, connectionCount.get(), clientIp);
            } else {
                log.debug("[CONNECTION-REMOVE] 未找到设备连接: IMEI={}", imei);
            }
        } catch (Exception e) {
            log.error("[CONNECTION-REMOVE] ❌ 移除设备连接失败: IMEI={}, Error={}",
                     imei, e.getMessage(), e);
        }
    }

    /**
     * 通过Channel移除设备连接
     * @param channel 网络通道
     */
    public void removeDeviceByChannel(Channel channel) {
        try {
            String channelId = channel.id().asShortText();
            String imei = channelImeiMap.remove(channelId);
            if (imei != null) {
                deviceChannels.remove(imei);
                log.info("Device {} connection removed by channel {}", imei, channelId);
            }
        } catch (Exception e) {
            log.error("Failed to remove device by channel: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取设备连接通道
     * @param imei 设备IMEI号
     * @return 网络通道
     */
    public Channel getDeviceChannel(String imei) {
        return deviceChannels.get(imei);
    }

    /**
     * 通过Channel获取设备IMEI
     * @param channel 网络通道
     * @return 设备IMEI号
     */
    public String getDeviceImei(Channel channel) {
        String channelId = channel.id().asShortText();
        return channelImeiMap.get(channelId);
    }

    /**
     * 检查设备是否在线
     * @param imei 设备IMEI号
     * @return 是否在线
     */
    public boolean isDeviceOnline(String imei) {
        Channel channel = deviceChannels.get(imei);
        return channel != null && channel.isActive();
    }

    /**
     * 发送指令到设备
     * @param imei 设备IMEI号
     * @param command 指令内容
     * @return 是否发送成功
     */
    public boolean sendCommandToDevice(String imei, String command) {
        try {
            log.info("[COMMAND-SEND] Preparing to send command: IMEI={}, Command={}", imei, command);

            Channel channel = deviceChannels.get(imei);
            if (channel == null) {
                log.warn("[COMMAND-SEND] ❌ Device not connected: IMEI={}", imei);
                return false;
            }

            if (!channel.isActive()) {
                log.warn("[COMMAND-SEND] ❌ Channel is inactive: IMEI={}, ChannelId={}",
                        imei, channel.id().asShortText());
                return false;
            }

            // 使用新的数据包构建器
            byte[] commandBytes = packetBuilder.buildCommandPacket(command);
            if (commandBytes.length == 0) {
                log.error("[COMMAND-SEND] ❌ Failed to build command packet: IMEI={}", imei);
                return false;
            }

            log.debug("[COMMAND-SEND] Command packet built: IMEI={}, PacketLength={}, Packet={}",
                     imei, commandBytes.length, packetBuilder.bytesToHexString(commandBytes));

            // 发送指令
            channel.writeAndFlush(Unpooled.wrappedBuffer(commandBytes));

            log.info(IotLogMarkers.IOT_COMMAND,
                    "[COMMAND-SEND] ✅ Command sent successfully: IMEI={}, Command={}, ChannelId={}",
                    imei, command, channel.id().asShortText());
            return true;

        } catch (Exception e) {
            log.error("[COMMAND-SEND] ❌ Failed to send command: IMEI={}, Command={}, Error={}",
                     imei, command, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送原始字节数据到设备
     * @param imei 设备IMEI号
     * @param data 原始数据
     * @return 是否发送成功
     */
    public boolean sendRawDataToDevice(String imei, byte[] data) {
        try {
            Channel channel = deviceChannels.get(imei);
            if (channel == null || !channel.isActive()) {
                log.warn("Device {} is not connected or channel is inactive", imei);
                return false;
            }

            // 发送原始数据
            channel.writeAndFlush(Unpooled.wrappedBuffer(data));
            
            log.debug("Raw data sent to device {}, length: {}", imei, data.length);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to send raw data to device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取在线设备数量
     * 阶段1增强：使用连接计数器提高性能
     * @return 在线设备数量
     */
    public int getOnlineDeviceCount() {
        return connectionCount.get();
    }

    /**
     * 阶段1增强：获取连接统计信息
     * @return 连接统计信息
     */
    public Map<String, Object> getConnectionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("total_connections", connectionCount.get());
        stats.put("max_connections", maxConnections);
        stats.put("connection_usage_rate", String.format("%.2f%%",
                (connectionCount.get() * 100.0 / maxConnections)));
        stats.put("ip_connections", ipConnectionCount.size());
        stats.put("active_channels", deviceChannels.size());

        // 统计每IP连接数
        Map<String, Integer> ipStats = new HashMap<>();
        ipConnectionCount.forEach((ip, count) -> ipStats.put(ip, count.get()));
        stats.put("connections_per_ip", ipStats);

        return stats;
    }

    /**
     * 阶段1增强：检查连接限制是否达到
     * @return 是否达到连接限制
     */
    public boolean isConnectionLimitReached() {
        return connectionCount.get() >= maxConnections;
    }

    /**
     * 阶段1增强：定时清理无效连接
     * 每分钟执行一次，清理已断开的连接
     */
    @Scheduled(fixedRate = 60000)
    public void cleanupInactiveConnections() {
        try {
            int cleanedCount = 0;
            Iterator<Map.Entry<String, Channel>> iterator = deviceChannels.entrySet().iterator();

            while (iterator.hasNext()) {
                Map.Entry<String, Channel> entry = iterator.next();
                Channel channel = entry.getValue();
                String imei = entry.getKey();

                if (!channel.isActive()) {
                    iterator.remove();

                    // 清理相关映射
                    String channelId = channel.id().asShortText();
                    channelImeiMap.remove(channelId);

                    // 更新计数器
                    connectionCount.decrementAndGet();

                    // 更新IP计数器
                    if (channel.remoteAddress() != null) {
                        String clientIp = extractClientIp(channel.remoteAddress().toString());
                        AtomicInteger ipCount = ipConnectionCount.get(clientIp);
                        if (ipCount != null) {
                            int remaining = ipCount.decrementAndGet();
                            if (remaining <= 0) {
                                ipConnectionCount.remove(clientIp);
                            }
                        }
                    }

                    cleanedCount++;
                    log.debug("[CONNECTION-CLEANUP] 清理无效连接: IMEI={}, ChannelId={}", imei, channelId);
                }
            }

            if (cleanedCount > 0) {
                log.info("[CONNECTION-CLEANUP] ✅ 清理完成: 清理{}个无效连接, 剩余连接数: {}",
                        cleanedCount, connectionCount.get());
            }

        } catch (Exception e) {
            log.error("[CONNECTION-CLEANUP] ❌ 清理无效连接时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 阶段1增强：从远程地址提取客户端IP
     * @param remoteAddress 远程地址字符串 (格式: /ip:port)
     * @return 客户端IP地址
     */
    private String extractClientIp(String remoteAddress) {
        if (remoteAddress == null || remoteAddress.isEmpty()) {
            return "unknown";
        }

        // 移除开头的斜杠
        if (remoteAddress.startsWith("/")) {
            remoteAddress = remoteAddress.substring(1);
        }

        // 提取IP部分（端口前的部分）
        int colonIndex = remoteAddress.lastIndexOf(':');
        if (colonIndex > 0) {
            return remoteAddress.substring(0, colonIndex);
        }

        return remoteAddress;
    }

    /**
     * 获取所有在线设备IMEI列表
     * @return 在线设备IMEI列表
     */
    public java.util.Set<String> getOnlineDeviceImeis() {
        return deviceChannels.entrySet().stream()
                .filter(entry -> entry.getValue().isActive())
                .map(java.util.Map.Entry::getKey)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 构建指令数据包 (已废弃，使用ProtocolPacketBuilder替代)
     * 根据GT06协议格式构建指令包
     * @param command 指令内容
     * @return 指令数据包
     * @deprecated 使用 {@link ProtocolPacketBuilder#buildCommandPacket(String)} 替代
     */
    @Deprecated
    private byte[] buildCommandPacket(String command) {
        log.warn("[DEPRECATED] buildCommandPacket method is deprecated, using ProtocolPacketBuilder instead");
        return packetBuilder.buildCommandPacket(command);
    }
}
