package com.yunqu.park.iot.protocol;

import com.yunqu.park.iot.config.IotProtocolConfig;
import com.yunqu.park.iot.utils.CrcUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 协议数据包构建器测试类
 * 
 * <p>测试不同兼容性模式下的数据包构建功能，确保与concox-master的兼容性。</p>
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2025-08-07
 */
@ExtendWith(MockitoExtension.class)
class ProtocolPacketBuilderTest {

    private ProtocolPacketBuilder packetBuilder;
    private IotProtocolConfig protocolConfig;

    @BeforeEach
    void setUp() {
        protocolConfig = new IotProtocolConfig();
        packetBuilder = new ProtocolPacketBuilder(protocolConfig);
    }

    @Test
    void testBuildConcoxCompatibleRebootPacket() {
        // 设置为CONCOX兼容模式
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.CONCOX);
        protocolConfig.getCrc().setEnabled(true);
        
        // 构建REBOOT指令数据包
        String command = "RESET#";
        byte[] packet = packetBuilder.buildCommandPacket(command);
        
        // 验证数据包不为空
        assertNotNull(packet);
        assertTrue(packet.length > 0);
        
        // 转换为十六进制字符串进行验证
        String hexPacket = packetBuilder.bytesToHexString(packet);
        
        // 验证起始位
        assertTrue(hexPacket.startsWith("7878"), "数据包应以7878开始");
        
        // 验证停止位
        assertTrue(hexPacket.endsWith("0D0A"), "数据包应以0D0A结束");
        
        // 验证协议号
        assertTrue(hexPacket.contains("80"), "数据包应包含协议号80");
        
        // 验证指令内容（RESET#的ASCII码）
        String expectedCommand = "524553455423"; // "RESET#"的十六进制
        assertTrue(hexPacket.contains(expectedCommand), "数据包应包含指令内容");
        
        System.out.println("CONCOX REBOOT packet: " + hexPacket);
    }

    @Test
    void testBuildConcoxCompatibleQueryStatusPacket() {
        // 设置为CONCOX兼容模式
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.CONCOX);
        protocolConfig.getCrc().setEnabled(true);
        
        // 构建QUERY_STATUS指令数据包
        String command = "STATUS#";
        byte[] packet = packetBuilder.buildCommandPacket(command);
        
        // 验证数据包不为空
        assertNotNull(packet);
        assertTrue(packet.length > 0);
        
        // 转换为十六进制字符串进行验证
        String hexPacket = packetBuilder.bytesToHexString(packet);
        
        // 验证基本结构
        assertTrue(hexPacket.startsWith("7878"), "数据包应以7878开始");
        assertTrue(hexPacket.endsWith("0D0A"), "数据包应以0D0A结束");
        assertTrue(hexPacket.contains("80"), "数据包应包含协议号80");
        
        // 验证指令内容（STATUS#的ASCII码）
        String expectedCommand = "5354415455532"; // "STATUS#"的十六进制
        assertTrue(hexPacket.contains(expectedCommand), "数据包应包含指令内容");
        
        System.out.println("CONCOX STATUS packet: " + hexPacket);
    }

    @Test
    void testBuildStandardPacketWithCrc() {
        // 设置为STANDARD模式
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.STANDARD);
        protocolConfig.getCrc().setEnabled(true);
        
        // 构建REBOOT指令数据包
        String command = "RESET#";
        byte[] packet = packetBuilder.buildCommandPacket(command);
        
        // 验证数据包不为空
        assertNotNull(packet);
        assertTrue(packet.length > 0);
        
        // 转换为十六进制字符串进行验证
        String hexPacket = packetBuilder.bytesToHexString(packet);
        
        // 验证基本结构
        assertTrue(hexPacket.startsWith("7878"), "数据包应以7878开始");
        assertTrue(hexPacket.endsWith("0D0A"), "数据包应以0D0A结束");
        
        // 验证不是0000的CRC（因为启用了CRC计算）
        assertFalse(hexPacket.contains("00000D0A"), "CRC不应该是0000");
        
        System.out.println("STANDARD REBOOT packet: " + hexPacket);
    }

    @Test
    void testBuildStandardPacketWithoutCrc() {
        // 设置为STANDARD模式，禁用CRC
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.STANDARD);
        protocolConfig.getCrc().setEnabled(false);
        
        // 构建REBOOT指令数据包
        String command = "RESET#";
        byte[] packet = packetBuilder.buildCommandPacket(command);
        
        // 验证数据包不为空
        assertNotNull(packet);
        assertTrue(packet.length > 0);
        
        // 转换为十六进制字符串进行验证
        String hexPacket = packetBuilder.bytesToHexString(packet);
        
        // 验证基本结构
        assertTrue(hexPacket.startsWith("7878"), "数据包应以7878开始");
        assertTrue(hexPacket.endsWith("0D0A"), "数据包应以0D0A结束");
        
        // 验证CRC为0000（因为禁用了CRC计算）
        assertTrue(hexPacket.contains("00000D0A"), "禁用CRC时应该是0000");
        
        System.out.println("STANDARD REBOOT packet (no CRC): " + hexPacket);
    }

    @Test
    void testCrcCalculationCompatibility() {
        // 测试CRC计算与concox-master的兼容性
        String testData = "12800a000000005245534554230020001";
        
        // 使用我们的CRC算法计算
        int calculatedCrc = CrcUtils.calculateCrcItuFromHexString(testData);
        
        // 验证CRC值不为0（说明计算了实际值）
        assertNotEquals(0, calculatedCrc, "CRC值不应该为0");
        
        // 验证CRC值在有效范围内
        assertTrue(calculatedCrc >= 0 && calculatedCrc <= 0xFFFF, "CRC值应该在0-65535范围内");
        
        System.out.println("Test data: " + testData);
        System.out.println("Calculated CRC: 0x" + String.format("%04X", calculatedCrc));
    }

    @Test
    void testSequenceNumberGeneration() {
        // 测试序列号生成
        protocolConfig.getSerialNumber().setAutoIncrement(true);
        protocolConfig.getSerialNumber().setStartValue(1);
        protocolConfig.getSerialNumber().setMaxValue(10);
        
        // 重置序列号计数器
        packetBuilder.resetSequenceCounter();
        
        // 生成多个数据包，验证序列号递增
        int initialSeq = packetBuilder.getCurrentSequenceNumber();
        
        packetBuilder.buildCommandPacket("TEST1#");
        int seq1 = packetBuilder.getCurrentSequenceNumber();
        
        packetBuilder.buildCommandPacket("TEST2#");
        int seq2 = packetBuilder.getCurrentSequenceNumber();
        
        // 验证序列号递增
        assertTrue(seq1 > initialSeq, "序列号应该递增");
        assertTrue(seq2 > seq1, "序列号应该继续递增");
        
        System.out.println("Initial sequence: " + initialSeq);
        System.out.println("Sequence 1: " + seq1);
        System.out.println("Sequence 2: " + seq2);
    }

    @Test
    void testEmptyCommandHandling() {
        // 测试空指令处理
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.CONCOX);
        
        // 测试null指令
        byte[] nullPacket = packetBuilder.buildCommandPacket(null);
        assertEquals(0, nullPacket.length, "null指令应该返回空数组");
        
        // 测试空字符串指令
        byte[] emptyPacket = packetBuilder.buildCommandPacket("");
        assertEquals(0, emptyPacket.length, "空字符串指令应该返回空数组");
        
        // 测试只有空格的指令
        byte[] spacePacket = packetBuilder.buildCommandPacket("   ");
        assertEquals(0, spacePacket.length, "只有空格的指令应该返回空数组");
    }

    @Test
    void testHexStringConversion() {
        // 测试十六进制字符串转换
        String testString = "RESET#";
        String expectedHex = "524553455423";
        
        // 通过构建数据包来间接测试ASCII转HEX功能
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.CONCOX);
        byte[] packet = packetBuilder.buildCommandPacket(testString);
        String hexPacket = packetBuilder.bytesToHexString(packet);
        
        // 验证包含期望的十六进制内容
        assertTrue(hexPacket.contains(expectedHex), 
                  "数据包应包含正确的十六进制指令内容: " + expectedHex);
    }

    @Test
    void testPacketLengthCalculation() {
        // 测试数据包长度计算
        protocolConfig.setCompatibilityMode(IotProtocolConfig.CompatibilityMode.CONCOX);
        protocolConfig.getCrc().setEnabled(true);
        
        // 构建不同长度的指令
        String shortCommand = "A#";
        String longCommand = "VERY_LONG_COMMAND_FOR_TESTING#";
        
        byte[] shortPacket = packetBuilder.buildCommandPacket(shortCommand);
        byte[] longPacket = packetBuilder.buildCommandPacket(longCommand);
        
        // 验证长指令的数据包确实更长
        assertTrue(longPacket.length > shortPacket.length, 
                  "长指令应该产生更长的数据包");
        
        System.out.println("Short command packet length: " + shortPacket.length);
        System.out.println("Long command packet length: " + longPacket.length);
    }

    @Test
    void testConfigValidation() {
        // 测试配置验证
        assertTrue(protocolConfig.isValid(), "默认配置应该是有效的");
        
        // 测试无效配置
        protocolConfig.getSerialNumber().setMaxValue(-1);
        assertFalse(protocolConfig.isValid(), "无效的序列号配置应该被检测出来");
        
        // 恢复有效配置
        protocolConfig.getSerialNumber().setMaxValue(65535);
        assertTrue(protocolConfig.isValid(), "修复后的配置应该是有效的");
    }
}
