{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {"mongodb": "^3.3.2"}, "repository": {"type": "git", "url": "https://github.com/mayank-mittal/mongodb.git"}, "description": "Mongodb Native - Promisified, Simplified", "license": "MIT", "main": "index.js", "name": "@intugine-technologies/mongodb", "scripts": {"test": "./node_modules/mocha/bin/mocha test/index.js"}, "keywords": ["mongo", "mongodb", "intugine", "promise"], "version": "1.12.5", "engines": {"node": ">=8.0.0"}, "devDependencies": {"mocha": "^6.2.1"}}