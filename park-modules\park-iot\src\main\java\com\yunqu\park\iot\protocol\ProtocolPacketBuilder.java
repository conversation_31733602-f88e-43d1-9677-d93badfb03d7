package com.yunqu.park.iot.protocol;

import com.yunqu.park.iot.config.IotProtocolConfig;
import com.yunqu.park.iot.utils.CrcUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 协议数据包构建器
 *
 * <p>负责根据配置的兼容性模式构建不同格式的IoT指令数据包。
 * 支持park-iot标准格式和concox-master兼容格式。</p>
 *
 * <h3>支持的格式：</h3>
 * <ul>
 *   <li><strong>STANDARD</strong>：park-iot原始格式（修复CRC后）</li>
 *   <li><strong>CONCOX</strong>：concox-master兼容格式（推荐）</li>
 * </ul>
 *
 * <h3>数据包结构：</h3>
 * <pre>
 * STANDARD格式：起始位(2) + 包长度(1) + 协议号(1) + 指令内容(N) + 序列号(2) + CRC(2) + 停止位(2)
 * CONCOX格式：  起始位(2) + 包长度(1) + 协议号(1) + 指令长度(1) + 服务器标志(4) + 指令内容(N) + 语言(2) + 序列号(2) + CRC(2) + 停止位(2)
 * </pre>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-08-07
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProtocolPacketBuilder {

    private final IotProtocolConfig protocolConfig;

    /**
     * 序列号计数器，用于生成递增的序列号
     */
    private final AtomicInteger sequenceCounter = new AtomicInteger(1);

    /**
     * 构建指令数据包
     * @param command 指令字符串
     * @return 数据包字节数组
     */
    public byte[] buildCommandPacket(String command) {
        if (command == null || command.trim().isEmpty()) {
            log.error("[PACKET-BUILD] ❌ Command is null or empty");
            return new byte[0];
        }

        try {
            return switch (protocolConfig.getCompatibilityMode()) {
                case CONCOX -> buildConcoxCompatiblePacket(command.trim());
                case STANDARD -> buildStandardPacket(command.trim());
            };
        } catch (Exception e) {
            log.error("[PACKET-BUILD] ❌ Failed to build packet: command={}, error={}",
                     command, e.getMessage(), e);
            return new byte[0];
        }
    }

    /**
     * 构建concox-master兼容的数据包
     */
    private byte[] buildConcoxCompatiblePacket(String command) {
        log.debug("[PACKET-BUILD] Building concox-compatible packet for command: {}", command);

        // 1. ASCII转HEX
        String commandHex = asciiToHex(command);
        log.debug("[PACKET-BUILD] Command hex: {}", commandHex);

        // 2. 构建协议组件
        String serverFlagBit = "00000000";  // 服务器标志位
        String lengthOfCommand = String.format("%02x", command.length() + 4);  // 指令长度+4
        String language = "0002";  // 语言设置
        String serial = generateSerialNumberHex();  // 序列号

        // 3. 组装信息内容
        String infoContent = lengthOfCommand + serverFlagBit + commandHex + language;
        log.debug("[PACKET-BUILD] Info content: {}", infoContent);

        // 4. 计算数据长度
        int dataLengthValue = 1 + infoContent.length() / 2 + 2 + 2;  // 协议号 + 信息内容 + 序列号 + CRC
        String dataLength = String.format("%02x", dataLengthValue);

        // 5. 组装数据包主体
        String data = dataLength + "80" + infoContent + serial;
        log.debug("[PACKET-BUILD] Data before CRC: {}", data);

        // 6. 计算CRC
        String crcHex = calculateCrcHex(data);
        log.debug("[PACKET-BUILD] Calculated CRC: {}", crcHex);

        // 7. 组装完整数据包
        String fullPacket = "7878" + data + crcHex + "0d0a";
        log.debug("[PACKET-BUILD] Full packet: {}", fullPacket);

        // 8. 转换为字节数组
        byte[] packetBytes = hexStringToBytes(fullPacket);
        log.info("[PACKET-BUILD] ✅ Concox-compatible packet built: length={}, command={}",
                packetBytes.length, command);

        return packetBytes;
    }

    /**
     * 构建标准数据包（原park-iot格式，但修复CRC）
     */
    private byte[] buildStandardPacket(String command) throws UnsupportedEncodingException {
        log.debug("[PACKET-BUILD] Building standard packet for command: {}", command);

        // 指令内容转字节
        byte[] commandBytes = command.getBytes(StandardCharsets.UTF_8);

        // 计算包长度（协议号 + 指令内容 + 序列号 + CRC）
        int packetLength = 1 + commandBytes.length + 2 + 2;

        // 构建数据包
        byte[] packet = new byte[2 + 1 + packetLength + 2];  // 起始位 + 包长度 + 内容 + 停止位
        int index = 0;

        // 起始位 0x7878
        packet[index++] = 0x78;
        packet[index++] = 0x78;

        // 包长度
        packet[index++] = (byte) packetLength;

        // 协议号（服务器指令下发）
        packet[index++] = (byte) 0x80;

        // 指令内容
        System.arraycopy(commandBytes, 0, packet, index, commandBytes.length);
        index += commandBytes.length;

        // 序列号
        int serialNumber = generateSerialNumber();
        packet[index++] = (byte) ((serialNumber >> 8) & 0xFF);  // 高字节
        packet[index++] = (byte) (serialNumber & 0xFF);         // 低字节

        // 构建用于CRC计算的数据（从包长度开始，不包含CRC）
        byte[] dataForCrc = new byte[index - 2];
        System.arraycopy(packet, 2, dataForCrc, 0, dataForCrc.length);

        // 计算并添加CRC
        if (protocolConfig.getCrc().isEnabled()) {
            int crc = CrcUtils.calculateCrcItu(dataForCrc);
            packet[index++] = (byte) (crc & 0xFF);        // CRC低字节
            packet[index++] = (byte) ((crc >> 8) & 0xFF); // CRC高字节
            log.debug("[PACKET-BUILD] CRC calculated: 0x{}", String.format("%04X", crc));
        } else {
            packet[index++] = 0x00;
            packet[index++] = 0x00;
            log.debug("[PACKET-BUILD] CRC disabled, using 0x0000");
        }

        // 停止位 0x0D0A
        packet[index++] = 0x0D;
        packet[index] = 0x0A;

        log.info("[PACKET-BUILD] ✅ Standard packet built: length={}, command={}",
                packet.length, command);

        return packet;
    }

    /**
     * ASCII字符串转十六进制
     */
    private String asciiToHex(String ascii) {
        StringBuilder hex = new StringBuilder();
        for (char c : ascii.toCharArray()) {
            hex.append(String.format("%02x", (int) c));
        }
        return hex.toString();
    }

    /**
     * 生成序列号
     */
    private int generateSerialNumber() {
        if (protocolConfig.getSerialNumber().isAutoIncrement()) {
            int current = sequenceCounter.getAndIncrement();
            if (current > protocolConfig.getSerialNumber().getMaxValue()) {
                sequenceCounter.set(protocolConfig.getSerialNumber().getStartValue());
                current = sequenceCounter.getAndIncrement();
            }
            return current;
        } else {
            return protocolConfig.getSerialNumber().getStartValue();
        }
    }

    /**
     * 生成序列号的十六进制字符串
     */
    private String generateSerialNumberHex() {
        int serialNumber = generateSerialNumber();
        return String.format("%04x", serialNumber);
    }

    /**
     * 计算CRC十六进制字符串
     */
    private String calculateCrcHex(String hexData) {
        if (!protocolConfig.getCrc().isEnabled()) {
            return "0000";
        }

        try {
            int crc = CrcUtils.calculateCrcItuFromHexString(hexData);
            return String.format("%04x", crc);
        } catch (Exception e) {
            log.error("[PACKET-BUILD] CRC calculation failed: {}", e.getMessage());
            return "0000";
        }
    }

    /**
     * 十六进制字符串转字节数组
     */
    private byte[] hexStringToBytes(String hexString) {
        if (hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex string length must be even");
        }

        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                                 + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * 字节数组转十六进制字符串（用于日志）
     */
    public String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b & 0xFF));
        }
        return sb.toString();
    }

    /**
     * 重置序列号计数器
     */
    public void resetSequenceCounter() {
        sequenceCounter.set(protocolConfig.getSerialNumber().getStartValue());
        log.info("[PACKET-BUILD] Sequence counter reset to {}", protocolConfig.getSerialNumber().getStartValue());
    }

    /**
     * 获取当前序列号
     */
    public int getCurrentSequenceNumber() {
        return sequenceCounter.get();
    }
}
